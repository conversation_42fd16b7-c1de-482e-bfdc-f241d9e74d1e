import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/element-ui.scss' // element-ui css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' //directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'


import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// 底部固定组件
import FooterBox from '@/components/FooterBox'
// 固定弹出层高度组件
import Jscrollbar from '@/components/Jscrollbar'
// 表格序号
import TableIndex from '@/components/TableIndex'
//ras加密
import JSEncrypt from 'jsencrypt'
//防止按钮多次点击
import preventReClick from './store/preventReClick' //防多次点击，重复提交
// 正确的导入方式
import { openFileView } from '@/utils/webOffice';

// 时间格式化UA
import moment from 'moment'//导入文件
// 无数据 卡片
import NoData from "@/components/NoData"



import * as filters from './plugins/filters.js'
Object.keys(filters).forEach(key=>{
    Vue.filter(key,filters[key])//插入过滤器名和对应方法
})

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.$moment = moment;

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('FooterBox', FooterBox)
Vue.component('Jscrollbar',Jscrollbar)
Vue.component('TableIndex',TableIndex)
Vue.component('NoData',NoData)


Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false


Vue.prototype.$getRsaCode = function(str){ // 注册方法
  let pubKey = `MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJQdzBISXloT15SHJTdPl0++ERC84XNMWWK5wEK3I8/SaFcqtse5/4kEPuNBgs6YwgzN5KJHrFCUNhh/xR54YEsCAwEAAQ==`;// ES6 模板字符串 引用 rsa 公钥
  let encryptStr = new JSEncrypt();
  encryptStr.setPublicKey(pubKey); // 设置 加密公钥
  let  data = encryptStr.encrypt(str.toString());  // 进行加密
  return data;
}
// 将 openFileView 方法挂载到 Vue 原型链上
Vue.prototype.openFileView = openFileView

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
