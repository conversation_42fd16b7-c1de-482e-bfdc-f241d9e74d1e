
<!-- 季度报告--首页 -->
<template>
  <div class="padding4 regular grayBackground ">
    <div class="row width height">

      <el-col v-if="orgGrade == 'G'" :span="8">


        <BlockCard
          title="上报总览"
        >
          <el-form>
            <div class="position">
              <div class="position-select">
                <el-row>
                  <el-col :span="24">
                    <el-form-item>
                      <div class="float-right">
                        <el-radio-group v-model="timeSelect" @change="timeSelectChange">
                          <el-radio label="1">本年</el-radio>
                          <el-radio label="2">累计</el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>

          <div class="ry-common-card-content" style="height: 90px;">
            <div class="early-warning-data">
              <div class="early-warning-data-li ">
                <el-row>
                  <div class="ewd-li ewd-li-5">
                    <div class="ewd-li-box">
                      <el-col :span="6">
                        <div class="ewd-li-5-left">
                          <p class="ewd-li-5-top text-center ewd-li-5-num problemNums margin-t10">
                            {{reportInfo.totalNum||'0'}}</p>
                          <p class="text-center">上报数</p>
                        </div>
                      </el-col>
                      <el-col :span="18">
                        <div class="ewd-li-5-right">
                          <el-row class="ewd-li-5-box">
                            <p class="ewd-li-5-percentage">进行中</p>
                            <b class="float-left ewd-li-5-box-b">{{reportInfo.statusNum1}}</b>
                            <div class="ewd-li-5-per">
                              <span class="ewd-li-5-span ewd-li-5-span-1"
                                    :style="{ width:reportInfo.statusRate1 + '%' }"></span>
                            </div>
                            <p class="ewd-li-5-percentage">{{reportInfo.statusRate1||0.00}}%</p>
                          </el-row>
                          <el-row class="ewd-li-5-box">
                            <p class="ewd-li-5-percentage">已完成</p>
                            <b class="float-left ewd-li-5-box-b">{{reportInfo.statusNum2}}</b>
                            <div class="ewd-li-5-per">
                              <span class="ewd-li-5-span ewd-li-5-span-1"
                                    :style="{ width:reportInfo.statusRate2 + '%' }"></span>
                            </div>
                            <p class="ewd-li-5-percentage">{{reportInfo.statusRate2||0.00}}%</p>
                          </el-row>
                        </div>
                      </el-col>
                    </div>
                  </div>
                </el-row>
              </div>
            </div>
          </div>
        </BlockCard>
        <BlockCard title="上报完成情况" >
          <el-form>
            <div class="position">
              <div  id="canvas" class="canvas">
                <echatsPie1 id="echars-1" :charsData="echarsList"  height="193px"></echatsPie1>
              </div>
              <div class="echart-text-box">
                <p class="echart-text-num">{{echartName}}</p>
                <p class="echart-text-num">{{echartPercent}}</p>
              </div>
              <div class="right-legend-box">
                <ul id="echartsList">
                  <li class="cursor" v-for="(item,index) in echarsList">
                    <div class="li-1 ovflowHidden">
                      <span class="span-radius"></span>
                      <span class="question-li-text">{{item.name}}</span>
                    </div>
                    <div class="li-2 text-center">{{item.value}}</div>
                    <div class="li-3 text-right">{{item.statusRate}}%</div>
                  </li>
                </ul>
              </div>
            </div>
          </el-form>
          <el-form>
             </el-form>
        </BlockCard>
        <BlockCard title="进行中报告" height="400">
          <Jscrollbar height="100%">
            <el-form ref="elForm" size="medium" label-width="0">
              <el-row>
                <el-col :span="24">
                  <ul id="rankList">
                    <li v-for="(item,index) in reportListData">
                      <div class="periodic-report-1 flex flex-between">
                        <div class="ovflowHidden">
                          <i class="text-red el-icon-document"></i>
                          <span>{{item.reportTitle}}</span>
                        </div>
                      </div>
                      <el-row class="periodic-report-2 layui-row">
                        <el-col :span="8" class="text-left">
                          <span class="span-title">已完成上报单位数：</span>
                          <span class="span-value">{{item.statusNum2}}</span>
                        </el-col>
                        <el-col :span="8" class="text-center">
                          <span class="span-title">进行中上报单位数：</span>
                          <span class="span-value">{{item.statusNum1}}</span>
                        </el-col>
                      </el-row>
                    </li>
                  </ul>
                </el-col>
              </el-row>
            </el-form>
          </Jscrollbar>
        </BlockCard>
      </el-col>
      <el-col :span="orgGrade != 'G'? 24: 16">
        <BlockCard
          title="上报问题列表"
          :height="850"
        >

          <el-form :model="queryParams" size="medium" ref="queryForm"
                   label-width="105px">
            <el-row>
              <el-col :span="8">
                <el-form-item label="上报年度">
                  <el-date-picker
                    format="yyyy"
                    value-format="yyyy"
                    v-model="searchData.reportYear"
                    type="year"
                    placeholder="请选择"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="上报季度">

                  <el-select
                    v-model="searchData.reportQuarter"
                    placeholder="请选择"
                    :style="{width: '100%'}"
                  >
                    <el-option
                      v-for="(item, index) in dict.type.REPORT_QUARTER"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="上报状态">
                  <el-select v-model="searchData.status" placeholder="请选择" :style="{width: '100%'}">
                    <el-option
                      v-for="(item, index) in dict.type.REPORT_STATUS"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-row>
            <div class="float-right" style="margin-bottom:10px;">
              <el-button

                v-if="orgGrade != 'A'"
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="addItem"
              >新增</el-button>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="tableList">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="clearInfo">重置</el-button>

            </div>
          </el-row>
          <el-table
            :data="tableData"
            border
            v-loading="tableLoading"
            style="width: 100%"
            height="650"
          >

            <el-table-column
              type="index"
              label="序号"
              align="center"
              sortable
              min-width="5%"
              :index="table_index"
            />
            <el-table-column
              label="上报标题"
              prop="reportTitle"
              min-width="35%"  show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div
                  style="text-align: left"
                  class="overflowHidden-1"
                  :title="scope.row.reportTitle"
                >
                  {{ scope.row.reportTitle || "" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="年度"
              prop="reportYear"
              min-width="5%"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="季度"
              align="center"
              prop="reportQuarter"
              min-width="10%"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{
                  scope.row.reportQuarter
                    | fromatComon(dict.type.REPORT_QUARTER)
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="发起时间"
              prop="createTime"
              align="center"
              min-width="15%"
              show-overflow-tooltip
            />
            <el-table-column
              label="状态"
              prop="status"
              min-width="10%"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{
                  scope.row.status | fromatComon(dict.type.REPORT_STATUS)
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="20%"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <!-- 集团编辑 -->
                <el-button
                  v-if="
                          orgGrade == 'G' && scope.row.editable == '1'
                        "
                  size="mini"
                  type="text"
                  title="编辑"
                  icon="el-icon-edit"
                  @click="editItem(scope.row)"
                >
                </el-button>


                <!-- 省分编辑 -->

                <el-button
                  v-if="
                          orgGrade != 'G' && scope.row.editable == '1'
                        "
                  size="mini"
                  type="text"
                  title="编辑"
                  icon="el-icon-edit"
                  @click="editProvItem(scope.row)"
                >
                </el-button>
                <!-- 集团查看-->

                <el-button
                  v-if="orgGrade == 'G'&&scope.row.quarterReportProvId"
                  size="mini"
                  type="text"
                  title="查看本部"
                  icon="el-icon-document-copy"
                  @click="openViewItem(scope.row)"
                >
                </el-button>

                <el-button
                  v-if="orgGrade == 'G'"
                  size="mini"
                  type="text"
                  title="查看汇总"
                  icon="el-icon-search"
                  @click="openViewAllItem(scope.row)"
                >
                </el-button>




                <!-- 省分查看 -->
                <el-button
                  v-if="orgGrade == 'P'"
                  size="mini"
                  type="text"
                  title="查看"
                  icon="el-icon-search"
                  @click="openViewProItem(scope.row)"
                >
                </el-button>
                <!-- 地市查看,与集团一样-->
                <el-button
                  v-if="orgGrade == 'A'"
                  size="mini"
                  type="text"
                  title="查看"
                  icon="el-icon-search"
                  @click="openViewItem(scope.row)"
                >
                </el-button>
                <!-- 汇总上报-集团经办人-状态为已完成2-汇总状态为未开始0 -->

                <el-button
                  v-if="summaryShowFlag &&scope.row.status == '2'&& (!scope.row.summaryStatus ||scope.row.summaryStatus == '0') "
                  size="mini"
                  type="text"
                  title="汇总上报"
                  icon="el-icon-document"
                  @click="openSummaryItem(scope.row)"
                >
                </el-button>
                <!-- 查看汇总上报-集团 -->
                <el-button
                  v-if="orgGrade == 'G' && scope.row.summaryStatus && scope.row.summaryStatus != '0' "
                  size="mini"
                  type="text"
                  title="查看汇总上报"
                  icon="el-icon-tickets"
                  @click="openViewSummary(scope.row)"
                >
                </el-button>
                <!-- <a
                  v-if="scope.row.status == '0'"
                  class="table-btn"
                  lay-event="delete"
                  @click="deleteItem(scope.row)"
                >
                  删除</a
                > -->
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="tableList"
          />
        </BlockCard>
      </el-col>
    </div>
    <!-- 集团-新增 -->

    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="addvisible"
      width="90%"
      title="新增"
      v-if="addvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "新增" }}</span>
      </div>

      <addProvince editType="add" :closeBtn="closeBtnAdd" editUnitType="group"/>
    </el-dialog>

    <!-- 集团-编辑 -->

    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="editvisible"
      width="90%"
      title="编辑"
      v-if="editvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "编辑" }}</span>
      </div>
      <addProvince editType="edit" :closeBtn="closeBtnEdit" editUnitType="group" :rowData="groupItem"/>
    </el-dialog>

    <!-- 省分-新增 -->

    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="addProvisible"
      width="90%"
      title="新增"
      @close="tableList"
      v-if="addProvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "新增" }}</span>
      </div>
      <addProvince editType="add" :closeBtn="closeBtnProAdd" editUnitType="province"/>
    </el-dialog>

    <!-- 省分-编辑 -->

    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="editProvisible"
      width="90%"
      @close="tableList"
      title="编辑"
      v-if="editProvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "编辑" }}</span>
      </div>
      <addProvince
        editType="edit"
        :closeBtn="closeBtnProEdit"
        :rowData="proItem"
        editUnitType="province"
      />
    </el-dialog>

    <!-- 集团查看 -->
    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="viewvisible"
      width="90%"
      title="查看"
      v-if="viewvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "查看" }}</span>
      </div>
      <viewGroup :rowData="groupItem" />
    </el-dialog>


      <!-- 集团查看汇总 -->
      <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="viewAllvisible"
      width="90%"
      title="查看汇总"
      v-if="viewAllvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "查看汇总" }}</span>
      </div>
      <viewGroup :rowData="groupItem" isAllData="isAllData"/>
    </el-dialog>

    <!-- 省分查看 -->
    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="viewProvisible"
      width="90%"
      title="查看"
      v-if="viewProvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "查看" }}</span>
      </div>
      <viewProvince :rowData="proItem" />
    </el-dialog>
    <!-- 汇总上报流程发起 -->
    <el-dialog
      class="commons_popup small-popup"
      v-bind="$attrs"
      :visible.sync="summaryVisible"
      width="90%"
      title="汇总上报"
      v-if="summaryVisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "汇总上报" }}</span>
      </div>
      <summary1 :closeBtn="closeBtnSummary" :rowData="summaryItem" :openType="openType" />
    </el-dialog>
    <!-- 汇总上报流程查看 -->
    <el-dialog
      class="commons_popup small-popup"
      v-bind="$attrs"
      :visible.sync="viewSummaryVisible"
      width="90%"
      title="汇总上报查看"
      v-if="viewSummaryVisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "汇总上报查看" }}</span>
      </div>
      <summary1 :closeBtn="closeBtnViewSummary" :rowData="viewSummaryItem" :openType="openType" />
    </el-dialog>
  </div>
</template>

<script>
import echarts from 'echarts';//图表
import echatsPie1 from "@/components/echars/echars-pie-1";
import addGroup from "@/views/quarterlyReport/add-group";
import addProvince from "@/views/quarterlyReport/add-province";
import viewGroup from "@/views/quarterlyReport/view-group";
import viewProvince from "@/views/quarterlyReport/view-province";
import summary1 from "@/views/quarterlyReport/summaryFlow/summary";

import {
  queryQuarterReportList,
  getQuarterReportCount,
  getQuarterReportGoing
} from "@/api/quarterly-report/homeIndex";

import BlockCard from '@/components/BlockCard';
export default {
  name: "QuarterlyReport/index",
  components: {
    BlockCard,
    echatsPie1,
    addGroup,
    addProvince,
    viewGroup,
    viewProvince,
    summary1,
  },
  dicts: ["REPORT_QUARTER", "REPORT_STATUS"],
  data() {
    return {
      echartName: '',
      echartPercent: '',
      orgGrade: "",
      summaryShowFlag: false,
      timeSelect: "1", //总览选择
      thisyear:'',
      //上报总览
      reportInfo:"",
      echarsList:[],
      // 进行中报告
      reportListData: [],
      //右侧查询条件
      searchData: {
        reportQuarter: "",
        reportYear: "",
        status: "",
      },
      //表格数据
      tableLoading: false, //表格loading
      tableData: [],
      //表格页码
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      //集团新增弹窗
      addvisible: false,
      //集团编辑弹窗
      editvisible: false,
      //集团编辑内容
      groupItem: {},
      //省分新增弹窗
      addProvisible: false,
      //省分编辑弹窗
      editProvisible: false,
      //省分编辑内容
      proItem: {},
      //集团查看弹窗
      viewvisible: false,
      //省分查看弹窗
      viewProvisible: false,
      //集团查看汇总
      viewAllvisible:false,
      summaryVisible: false,
      //汇总上报
      summaryItem:{},
      viewSummaryVisible: false,
      viewSummaryItem:{},
      //打开汇总上报页面类型：查看，发起汇总上报
      openType: ''
    };
  },
  created() {
    this.thisyear = new Date().getFullYear()
    this.orgGrade = this.$store.getters.orgGrade;
    if (this.orgGrade === "G"){
      this.getQuarterReportCount()
      this.getQuarterReportGoing()
    }
    const roles = this.$store.getters.roles
    this.summaryShowFlag =  roles.indexOf("JTJBR")>1
    this.tableList();
  },
  methods: {
    table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
    //进行中报告
    getQuarterReportGoing() {
      getQuarterReportGoing({ thisYear: this.thisyear  }).then((response) => {
        this.reportListData  = response.data
      });
    },
    // 自适应字体大小
    fontSizeFun(res) {
      let docEl = document.documentElement,
        clientWidth =
          window.innerWidth ||
          document.documentElement.clientWidth ||
          document.body.clientWidth;
      if (!clientWidth) return;
      let fontSize = 100 * (clientWidth / 1920);
      return res * fontSize;
    },
    //上报总览
    getQuarterReportCount() {
      getQuarterReportCount({ thisYear: this.thisyear  }).then((response) => {
        this.reportInfo = response.data
        this.echarsList = []
      const  statusNum1 =  this.reportInfo.statusNum1||0
      const  statusNum2 =  this.reportInfo.statusNum2||0
      const statusRate1 = this.reportInfo.statusRate1||0
      const statusRate2 = this.reportInfo.statusRate2||0
        this.echarsList.push({
          value: statusNum1||'0',
          name: '进行中',
          statusRate:statusRate1,
          itemStyle: { color: "#69c0ff" },
          name2:'进行中'
        })

        this.echarsList.push({
          value: statusNum2||'0',
          name: '已完成',
          statusRate:statusRate2,
          itemStyle: { color: "#ffc069" },
          name2:'已完成'
        })


        this.echartName = '已完成';
        this.echartPercent =statusRate2+'%';
        const chartDom = document.getElementById("canvas");
        const commonMyChart = echarts.init(chartDom);
        const colorlist = [
          "#ffc069",
          "#69c0ff",
        ];
        let option = {
          color: colorlist,
          tooltip: {
            trigger: "item",
          },
          grid: {
            top: this.fontSizeFun(0.20)
          },
          calculable: true,
          series: [
            {
              name: "上报完成情况",
              type: "pie",
              radius: ["50%", "75%"], //环形饼状图
              center: ["23%", "50%"],
              label: {
                show: false,
              },
              data: this.echarsList,
            },
          ],
        };
        option && commonMyChart.setOption(option);
        commonMyChart.on('click', (e) => {
          this.echartName = e.name;
          this.echartPercent = e.percent + '%';
        })

      });
    },
    //上报总览选择
    timeSelectChange(val) {
      if(val=='1'){
        this.thisyear =  new Date().getFullYear()
      }
      if(val == '2'){
        this.thisyear = ''
      }
      this.getQuarterReportCount()
    this.getQuarterReportGoing()
    },

    //表格刷新
    tableList() {
      this.tableLoading = true;
      console.info("this.searchData:");
      console.info(this.searchData);
      queryQuarterReportList(this.searchData,this.queryParams).then((response) => {
        this.tableData = response.rows;
        this.total = response.total;
        this.tableLoading = false;
      });
    },
    //重置
    clearInfo() {
      this.queryParams.pageNum = 1;
      this.searchData.reportQuarter = "";
      this.searchData.reportYear = "";
      this.searchData.status = "";
      this.tableList();
    },
    //表格删除
    deleteItem(row) {
      this.$modal
        .confirm("是否确定删除该数据？")
        .then(function () {
          return violDailyDraftDelete({ id: row.id });
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.tableList();
        })
        .catch(() => {});
    },
    //新增
    addItem() {

      if(this.orgGrade != 'G' ){
         //省分新增
        this.addProvisible = true;
      }else{
  //  集团新增
      this.addvisible = true;
      }
    },
    //集团编辑
    editItem(row) {
      this.editvisible = true;
      this.groupItem = row;
    },
    //关闭新增(集团)
    closeBtnAdd() {
      this.addvisible = false;
      this.tableList()
    },
    //关闭编辑(集团)
    closeBtnEdit() {
      this.editvisible = false;
      this.tableList()
    },
    //省分编辑
    editProvItem(row) {
      this.editProvisible = true;
      this.proItem = row;
    },
    //关闭新增(省分)
    closeBtnProAdd() {
      this.addProvisible = false;
      this.tableList()
    },
    //关闭编辑(省分)
    closeBtnProEdit() {
      this.editProvisible = false;
      this.tableList()
    },

    //集团查看
    openViewItem(row) {
      this.viewvisible = true;
      this.groupItem = row;
    },
    //集团 查看汇总
    openViewAllItem(row){
      this.viewAllvisible = true;
      this.groupItem = row;
    },
    //省分查看
    openViewProItem(row) {
      this.viewProvisible = true;
      this.proItem = row;
    },
    //汇总流程发起页面
    openSummaryItem(row){
      this.summaryVisible = true;
      this.summaryItem = row
      this.openType = 'add'
    },
    closeBtnSummary(){
      this.summaryVisible = false;
      this.tableList()
    },
    openViewSummary(row){
      this.viewSummaryVisible = true;
      this.viewSummaryItem = row
      this.openType = 'view'
    },
    closeBtnViewSummary(row){
      this.viewSummaryVisible = false;
      this.tableList()
    },

    //表格删除
    deleteItem(row) {
      this.$modal
        .confirm("是否确定删除该数据？")
        .then(function () {
          return violDailyDraftDelete({ id: row.id });
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style rel="stylesheet/scss"  lang="scss" scoped>
 .small-popup ::v-deep .el-dialog{
  margin-bottom:0;
}
.regular{
  .el-dialog__body{
    height: 71vh;
  }

  .scope-chao-shi{
    margin-right:5px;
    margin-bottom: 0;
  }
  .canvas {
    width: 100%;
    height: 194px;
    z-index: 10;
  }

  .echart-text-box {
    position: absolute;
    width: calc((100% - 40px) / 2);
    height: 100px;
    top: 64px;
    z-index: 9;
    text-align: center;
    p {
      text-align: center;
      line-height: 32px;
    }
    .echart-text-p {
      font-size: 12px;
    }
  }

  .early-warning-data {
    margin-bottom: 10px;
  }

  .early-warning-data-li {
    width: 100%;
    margin: 4px 0;
  }

  .ewd-li {
    float: left;
    padding: 4px;
    box-sizing: border-box;
    height: 80px;
  }

  .ewd-li-box {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: solid 1px #eeeeee;
    height: 100%;
    padding: 8px;
    box-sizing: border-box;
  }

  .ewd-li-1 {
    width: 83px;
  }

  .ewd-li-1 .ewd-li-1-circular {
    width: 68px;
    height: 68px;
    display: inline-block;
    line-height: 68px;
    border-radius: 50%;
    text-align: center;
    font-size: 18px;
    color: #ffffff;
    margin-top: 8px;
  }

  .circular-1 {
    background-image: linear-gradient(-30deg,
      #ff4d4e 0%,
      #ffa39d 100%);
  }

  .circular-2 {
    background-image: linear-gradient(-30deg,
      #40a9ff 0%,
      #91d5ff 100%);
  }

  .ewd-li-right {
    float: right;
    width: calc(100% - 83px);
  }

  .ewd-li-2 {
    width: 30%;
  }

  .ewd-li-3 {
    width: 50%;
  }

  .ewd-li-4 {
    width: 20%;
  }

  .ewd-li-5 {
    width: 100%;
    padding: 0;
  }

  .ewd-li-6 {
    padding: 0;
  }

  .ewd-li-5 .ewd-li-box, .ewd-li-6 .ewd-li-box {
    padding: 10px 20px 10px 0px;
  }

  .ewd-li-5-left {
    height: 100%;
    border-right: 1px solid #d9d9d9;
  }

  .ewd-li-5-right {
    height: 100%;
    padding-left: 20px;
    box-sizing: border-box;
  }

  .ewd-li-5-top {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #73777a;
    margin-bottom:0;
  }

  .ewd-li-5-num {
    font-size: 20px;
    line-height: 24px;
    color: #181818;
  }

  .ewd-li-6-num {
    font-size: 18px;
    line-height: 24px;
    color: #333333;
  }

  .ewd-li-6-title {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #898D8F;
    margin-bottom: 10px;
  }

  .ewd-li-5-box-img {
    margin-right: 10px;
  }

  .ewd-li-5-box-b {
    padding-left: 10px;
    font-size: 18px;
    margin-right:0;
    width: 50px;
  }

  .ewd-li-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    height: 40px;
  }

  .ewd-li-5-box {
    margin:5px 0;
    line-height: 24px;
  }

  .ewd-li-5-percentage {
    float: left;
    font-size: 14px;
  }

  .ewd-li-5-per {
    float: left;
    width: 26%;
    margin: 3px 10px 0 10px;
    height: 18px;
    background-color: #f5f5f5;
    border-radius: 2px;
  }

  .ewd-li-5-span {
    height: 18px;
    display: inline-block;
    border-radius: 2px;
  }

  .ewd-li-5-span-1 {
    background-color: #ff4d4e;
  }

  .ewd-li-5-span-2 {
    background-color: #ffa940;
  }

  .ewd-li-5-span-3 {
    background-color: #ff8787;
  }

  .ewd-li-5-span-4 {
    background-color: #8da2fe;
  }

  .ewd-li-top .ewd-li-top-left .iconfont {
    font-size: 20px;
  }

  .ewd-li-top-left {
    display: flex;
    align-items: center;
  }

  .nodara {
    width: 100%;
    height: 100%;
    min-height: 130px;
    color: #b5b5b5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ewd-li-top .ewd-li-top-left .icon-processed {
    font-size: 16px;
  }

  .ewd-li-top .ewd-li-top-left span {
    font-size: 14px;
    color: #000;
    margin-left: 4px;
    line-height: 14px;
  }

  .ewm-conter-li-box {
    display: flex;
    border-radius: 2px;
    overflow: hidden;
  }

  .ewd-li-box .ry-form-radio, .early-warning-model .ry-form-radio {
    margin: 0;
  }

  .ry-form-radioed > i, .ry-form-radio > i:hover {
    color: #f5222d
  }

  .right-btn-box .ry-form-radio {
    margin-top: 0;
    padding: 0;
  }

  .ewd-li-top-right span {
    font-size: 22px;
    font-weight: bold;
    color: #333333;
  }

  .ewd-li-bottom {
    width: 100%;
    display: flex;
    height: 28px;
    justify-content: space-between;
    align-items: center;
  }

  .ewd-li-bottom .ewd-li-bottom-li-label {
    font-size: 12px;
    color: #888888;
  }

  .ewd-li-bottom .ewd-li-bottom-li-num {
    font-size: 14px;
    color: #f5212d;
  }

  .ewd-li-bottom .ewd-li-bottom-li-model {
    font-size: 12px;
    background-color: #eeeeee;
    border-radius: 4px;
    border: solid 1px #cccccc;
    color: #888888;
    padding: 2px 12px;
  }

  .ewd-li-bottom .ewd-li-bottom-li-value {
    font-size: 14px;
    color: #03ac2b;
  }

  .ry-quarantine {
    width: 100%;
    height: 13px;
    background-color: #eeeeee;
  }

  .early-warning-model-box {
    width: 100%;
    height: 466px;
    position: relative;
  }

  .early-warning-model-list {
    width: 100%;
    height: 466px;
    position: relative;
  }

  .early-warning-model-box:before {
    position: absolute;
    content: '\7cbe\51c6\5ea6';
    height: calc(100% - 20px);
    left: 50%;
    top: 10px;
    width: 1px;
    background: #cccccc;
    display: flex;
    justify-content: center;
    padding-top: 30px;
    box-sizing: border-box;
    color: #888888;
  }

  .early-warning-model-box:after {
    position: absolute;
    content: "\91cd\8981\6027";
    width: calc(100% - 20px);
    top: 50%;
    left: 10px;
    height: 1px;
    background: #cccccc;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 30px;
    box-sizing: border-box;
    color: #888888;
  }

  .ewm-box-ul {
  }

  .ewm-box-ul-li {
    height: 233px;
    box-sizing: border-box;
    position: relative;
  }

  .ewm-box-ul-li1 {
    padding: 20px 0 20px 20px;
  }

  .ewm-box-ul-li2 {
    padding: 20px 10px 20px 0;
    position: relative;
  }

  .ewm-box-ul-li3 {
    padding: 35px 0 20px 20px;
  }

  .ewm-box-ul-li4 {
    padding: 35px 10px 20px 0;
  }

  .ewm-box-ul-li1:after {
    content: "";
    position: absolute;
    left: -7px;
    top: 5px;
    width: 0;
    height: 0;
    border-width: 0 8px 8px;
    border-style: solid;
    border-color: transparent transparent #888888;
  }

  .ewm-box-ul-li2:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    left: 0;
    bottom: -5px;
    border-radius: 50%;
    border: 1px solid #888888;
  }

  .ewm-box-ul-li3:after {
    content: "";
    position: absolute;
    right: -6px;
    top: -7px;
    width: 0;
    height: 0;
    border-width: 8px 8px 8px;
    border-style: solid;
    border-color: transparent transparent transparent #888888;
  }

  .ewm-box-ul-li4:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    right: -5px;
    bottom: 0;
    border-radius: 50%;
    border: 1px solid #888888;
  }

  .ewm-conter-list {
    height: 180px;
    overflow: auto;
    padding-right: 10px;
  }

  .ewm-conter-li {
    width: 100%;
    height: 20px;
    margin: 10px 0;
    cursor: pointer;
  }

  .ewm-conter-li.active .ewm-conter-li-title {
    color: #f5222d;
  }

  .ewm-conter-li-title {
    padding: 0;
    float: left;
    line-height: 20px;
    width: 32%;
    font-size: 13px;
  }

  .ewm-conter-li-right {
    float: right;
    width: 68%;
  }

  .ewm-conter-li-data {
    width: 46px;
    float: left;
    line-height: 20px;
    padding-left: 2px;
    box-sizing: border-box;
  }

  .ewm-conter-li-data .iconfont {
    font-size: 14px;
    vertical-align: middle;
  }

  .ewm-conter-li-num {
    font-size: 14px;
    color: #333333;
    vertical-align: top;
  }

  .ewm-conter-li-speed {
    float: right;
    width: calc(100% - 46px);
  }

  .ewm-conter-li-value {
    box-sizing: border-box;
    float: left;
    height: 20px;
    color: #ffffff;
    font-size: 14px;
    line-height: 20px;
  }

  /*******************  */
  .ewm-conter-li-value1 {
    text-align: center;
    background-image: linear-gradient(90deg,
      rgba(3, 172, 43, 0.8) 0%,
      rgba(3, 172, 43, 0.3) 100%);
    border-radius: 2px 0px 0px 2px;
  }

  .ewm-conter-li-value2 {
    text-align: center;
    background-image: linear-gradient(90deg,
      rgba(250, 139, 22, 0.8) 0%,
      rgba(250, 139, 22, 0.3) 100%);
    border-radius: 0px 2px 2px 0px;
  }

  .drill-model-list {
  }

  .drill-model-li {
    display: flex;
    align-items: center;
    height: 38px;
  }

  .drill-model-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
  }

  .drill-model-span1 {
    color: #73777a;
  }

  .drill-model-span2 {
    color: #181818;
    margin-right: 8px;
  }

  .ewm-conter-li-values1 {
    text-align: left;
    background: #ffb180;
    padding-left: 6px;
  }

  .ewm-conter-li-values2 {
    padding-left: 8px;
    text-align: left;
    background: #5ad8a6;
  }

  /*******************  */
  .ewm-conter-fixed {
    position: absolute;
  }

  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }

  .ewm-conter-fixed-title {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #555555;
    border: solid 2px #cccccc;
    display: inline-block;
    border-radius: 50%;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    z-index: 999;
  }

  .ewm-conter-fixed:hover .ewm-conter-fixed-data {
    display: inline-block;
  }

  .ewm-conter-fixed-data {
    display: none;
    height: 20px;
    background-color: #888888;
    border-radius: 10px 9px 9px 10px;
    border: solid 1px #cccccc;
    width: 110px;
    text-align: center;
    position: absolute;
    right: 15px;
    top: 4px;
    z-index: 0;
    line-height: 20px;
  }

  .ewm-conter-fixed1 {
    left: 10px;
    bottom: 10px;
  }

  .ewm-conter-fixed1 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
  }

  .ewm-conter-fixed2 {
    right: 10px;
    bottom: 10px;
  }

  .ewm-conter-fixed2 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
  }

  .ewm-conter-fixed3 {
    left: 10px;
    top: 10px;
  }

  .ewm-conter-fixed3 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
  }

  .ewm-conter-fixed4 {
    right: 10px;
    top: 10px;
  }

  .ewm-conter-fixed4 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
  }

  .ewm-conter-fixed-data .iconfont {
    color: #fff;
    font-size: 10px;
    margin-left: 5px;
  }

  .right-btn-box .ry-form-switch {
    margin-top: -2px;
  }

  .ewm-conter-fixed-data span {
    color: #fff;
  }



  .right-legend-box {
    width: calc(50% - 20px);
    position: absolute;
    right: 20px;
    top: 0;
    height: 100%;
    overflow: auto;
    display: flex;
    align-items: center;
    z-index: 10;
  }

  .right-legend-box ul {
    width: 100%;
  }

  .right-legend-box li {
    width: 100%;
    display: inline-block;
    line-height: 24px;
    color: #73777a;
  }

  .right-legend-box li .li-1 {
    float: left;
    width: 40%;
  }

  .right-legend-box li .li-1 .span-radius {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #ffb180;
    border-radius: 50%;
    margin-right: 6px;
  }

  .right-legend-box li .li-2 {
    float: left;
    width: 30%;
  }

  .right-legend-box li .li-3 {
    float: left;
    width: 30%;
  }
  .cursor{
    cursor: pointer;
  }

  .periodic-report-1, .periodic-report-2 {
    line-height: 32px;
  }

  .periodic-report-2 .span-title {
    color: #A8A4A4;
  }

  .flex {
    display: flex;
    align-items: center;
  }

  .flex-between {
    justify-content: space-between;
  }
}
</style>







