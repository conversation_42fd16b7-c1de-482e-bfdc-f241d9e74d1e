//可用，进入自己选票并确认。
//优化版本 - 提高抢票成功率

// 设置脚本运行参数，提高性能
auto.setMode("fast"); // 设置为快速模式
auto.setFlags(["findOnUiThread"]); // 在UI线程中查找控件，提高速度

 // 检查无障碍服务是否已经启用，如果没有启用则跳转到无障碍服务启用界面，并等待无障碍服务启动；当无障碍服务启动后脚本会继续运行。
 auto.waitFor();
 //打开猫眼app
 app.launchApp("猫眼");
 openConsole();
 console.setTitle("猫眼 go!","#ff11ee00",30);
 
main();
 
//获取输入的场次信息
//function getPlayEtc(){
 //   var playEtc = rawInput("请输入场次关键字(按照默认格式)", "周六");
//    if (playEtc == null || playEtc.trim()=='') {
//        alert("请输入场次信息!");
//        return getPlayEtc();
 //   }
 //   console.log("手动输入的场次信息："+playEtc);
//    return playEtc;
//}
 className("android.widget.TextView").text("确认").waitFor();
//获取输入票价信息
function getTicketPrice(){
    var ticketPrice = rawInput("请输入票价关键字(按照默认格式)", "788");
    if (ticketPrice == null || ticketPrice.trim()=='') {
        alert("请输入票价信息!");
        return getTicketPrice();
     }
 
    console.log("手动输入的票价信息："+ticketPrice);
    return ticketPrice;
}
 
//获取输入的抢票时间
function getSellTime(){
    var sellTime = rawInput("请输入抢票时间(按照默认格式)", "03-01 20:00");
    if (sellTime == null || sellTime.trim()=='') {
        alert("请输入抢票时间!");
        return getSellTime();
     }
    return sellTime;
}
 
 
function main() {
    console.log("开始猫眼抢票!");
    var preBook= text("已 预 约").findOne(2000)
    var preBook2 = className("android.view.View").text("已填写").findOne(2000)
    var isPreBook = preBook2!=null||preBook!=null;
    console.log("界面是否已预约："+isPreBook);
 //   if(!isPreBook){
 //       console.log("无预约信息，请输入抢票信息!");
 //       playEtc = getPlayEtc();
  //      ticketPrice = getTicketPrice();
 //   }
 
    var month;
    var day;
    var hour;
    var minute;
 
    var inputTime = getSellTime();
    //在这里使用输入的时间进行后续操作
    console.log("输入的抢票时间：" + inputTime);
    var times = inputTime.split(" ");
    var time1 = times[0]
    var time2 = times[1]
    var monthDay= time1.split("-");
    month = monthDay[0] - 1;
    day = monthDay[1];
    var hourMinute= time2.split(":");
    hour = hourMinute[0];
    minute=  hourMinute[1];
 
    // 设置开抢时间
    var year = new Date().getFullYear();
    var second = 0;
    var msecond = 0;
    var startTimestamp = new Date(year, month, day, hour, minute, second, msecond).getTime();
     // 减去 200ms 的网络延迟和反应时间，提前抢票
    startTimestamp = startTimestamp - 200;
    var damaiTimestamp;
    var startTime = convertToTime(startTimestamp);
    console.log("开抢时间：", startTime);
    console.log("等待开抢...");
    // 循环等待，优化等待策略
    while (true) {
        damaiTimestamp = getDamaiTimestamp();

        // 距离开抢时间还有多久
        var timeLeft = startTimestamp - damaiTimestamp;

        if (damaiTimestamp >= startTimestamp) {
            break;
        }

        // 根据剩余时间调整等待间隔，越接近开抢时间等待间隔越短
        if (timeLeft > 10000) {
            sleep(1000); // 超过10秒，等待1秒
        } else if (timeLeft > 5000) {
            sleep(500);  // 5-10秒，等待0.5秒
        } else if (timeLeft > 1000) {
            sleep(100);  // 1-5秒，等待0.1秒
        } else {
            sleep(10);   // 最后1秒，等待0.01秒
        }
    }
 
    var realStartTime = getDamaiTimestamp();
    console.log("冲啊！！！");

    // 优化按钮查找和点击逻辑，提高成功率
    var maxAttempts = 50; // 最大尝试次数
    var attempt = 0;
    var clicked = false;

    while(!clicked && attempt < maxAttempts){
        attempt++;

        // 同时查找多个可能的按钮文本
        var buttons = [
            className("android.widget.TextView").text("立即预订"),
            className("android.widget.TextView").text("立即购票"),
            className("android.widget.TextView").text("特惠购票"),
            className("android.widget.TextView").text("马上抢"),
            className("android.widget.TextView").text("立即抢购"),
            className("android.view.View").text("立即预订"),
            className("android.view.View").text("立即购票")
        ];

        // 尝试点击找到的第一个按钮
        for(var i = 0; i < buttons.length; i++){
            var button = buttons[i].findOne(100); // 100ms超时
            if(button != null){
                var clickResult = button.click();
                console.log("第" + attempt + "次尝试，点击按钮：" + buttons[i].text() + "，结果：" + clickResult);
                if(clickResult){
                    clicked = true;
                    break;
                }
            }
        }

        // 如果没有找到按钮，短暂等待后重试
        if(!clicked){
            sleep(20); // 减少等待时间，提高响应速度
        }
    }

    if(!clicked){
        console.log("警告：经过" + maxAttempts + "次尝试仍未找到购票按钮！");
    }
    
 //   if(!isPreBook){
        // 选择场次
      //  textContains(playEtc).findOne().parent().click();
//        console.log("准备选择场次");
    //    className("android.widget.TextView").text("2024-02-25 周日 19:00").findOne().click();
 //       console.log("选择场次");
     //   className("android.widget.TextView").text("晚场-VIP票 ¥399").findOne().click();
  //      console.log("选择票档");
    //}
    //className("android.view.View").text("确认").waitFor();
  //  className("android.widget.TextView").text("确认").findOne().click();
  // className("android.widget.TextView").text("确认").waitFor();
    // 优化确认按钮点击
    var confirmClicked = false;
    var confirmAttempts = 0;
    while(!confirmClicked && confirmAttempts < 10){
        confirmAttempts++;
        var confirmBtn = className("android.widget.TextView").text("确认").findOne(500);
        if(confirmBtn != null){
            confirmClicked = confirmBtn.click();
            console.log("第" + confirmAttempts + "次点击确认按钮：" + confirmClicked);
        }
        if(!confirmClicked){
            sleep(50);
        }
    }

    //等待立即支付按钮出现，增加超时时间
    var payButtonFound = className("android.widget.Button").waitFor(5000);
    if(!payButtonFound){
        console.log("警告：未找到支付按钮，尝试查找其他支付相关按钮");
        // 尝试查找其他可能的支付按钮
        var altPayButtons = [
            className("android.widget.TextView").text("立即支付"),
            className("android.view.View").text("立即支付"),
            className("android.widget.Button").textContains("支付"),
            className("android.widget.TextView").textContains("支付")
        ];

        for(var i = 0; i < altPayButtons.length; i++){
            var altBtn = altPayButtons[i].findOne(1000);
            if(altBtn != null){
                payButtonFound = true;
                break;
            }
        }
    }

    if(payButtonFound){
        // 快速连续点击支付按钮，提高成功率
        var payClickCount = 0;
        var maxPayClicks = 20;

        while(payClickCount < maxPayClicks){
            var payButtons = [
                className("android.widget.Button"),
                className("android.widget.TextView").text("立即支付"),
                className("android.view.View").text("立即支付")
            ];

            var payClicked = false;
            for(var i = 0; i < payButtons.length; i++){
                var payBtn = payButtons[i].findOne(100);
                if(payBtn != null){
                    var clickResult = payBtn.click();
                    if(clickResult){
                        payClickCount++;
                        console.log("第" + payClickCount + "次点击支付按钮成功");
                        payClicked = true;
                        break;
                    }
                }
            }

            if(!payClicked){
                break; // 如果没有找到支付按钮，退出循环
            }

            sleep(50); // 短暂等待
        }

        var t = getDamaiTimestamp() - realStartTime;
        console.log("总花费时间：" + t + "ms");
    } else {
        console.log("错误：未找到任何支付按钮！");
    }
    
    console.log("结束时间："+convertToTime(getDamaiTimestamp()))
 
 
}
 
/**
 * @returns 大麦服务器时间戳，增加错误处理和重试机制
 */
function getDamaiTimestamp() {
    var maxRetries = 3;
    var retryCount = 0;

    while(retryCount < maxRetries){
        try {
            var response = http.get("https://mtop.damai.cn/gw/mtop.common.getTimestamp/", {
                headers: {
                    'Host': 'mtop.damai.cn',
                    'Content-Type': 'application/json;charset=utf-8',
                    'Accept': '*/*',
                    'User-Agent': 'floattime/1.1.1 (iPhone; iOS 15.6; Scale/3.00)',
                    'Accept-Language': 'zh-Hans-CN;q=1, en-CN;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive'
                },
                timeout: 3000 // 3秒超时
            });

            if(response && response.body){
                var result = JSON.parse(response.body.string());
                if(result && result.data && result.data.t){
                    return result.data.t;
                }
            }
        } catch(e) {
            console.log("获取时间戳失败，第" + (retryCount + 1) + "次重试：" + e);
        }

        retryCount++;
        if(retryCount < maxRetries){
            sleep(100); // 重试前等待100ms
        }
    }

    // 如果所有重试都失败，使用本地时间戳
    console.log("警告：无法获取服务器时间戳，使用本地时间");
    return new Date().getTime();
}
 
/**
 * @param {时间戳} timestamp 
 * @returns ISO 8601 格式的北京时间
 */
function convertToTime(timestamp) {
    var date = new Date(Number(timestamp));
    var year = date.getUTCFullYear();
    var month = (date.getUTCMonth() + 1).toString().padStart(2, "0");
    var day = date.getUTCDate().toString().padStart(2, "0");
    var hours = (date.getUTCHours() + 8).toString().padStart(2, "0");
    var minutes = date.getUTCMinutes().toString().padStart(2, "0");
    var seconds = date.getUTCSeconds().toString().padStart(2, "0");
    var milliseconds = date.getUTCMilliseconds().toString().padStart(3, "0");
    var iso8601 = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    return iso8601;
}
 
 
 //点击控件所在坐标
function btn_position_click(x) {
    if (x) {
       var b = x.bounds();
       print(b.centerX())
       print(b.centerY())
       var c = click(b.centerX(), b.centerY()) 
 
       console.log("点击是否成功："+c);
    }
}